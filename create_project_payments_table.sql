-- Create project_payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS `project_payments` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `project_id` int(11) unsigned NOT NULL,
    `payment_amount` decimal(15,2) NOT NULL,
    `payment_method` enum('cash','check','bank_transfer','credit_card','other') DEFAULT 'cash',
    `payment_status` enum('pending','partial','completed','overdue') DEFAULT 'pending',
    `payment_date` date DEFAULT NULL,
    `payment_notes` text DEFAULT NULL,
    `invoice_number` varchar(100) DEFAULT NULL,
    `created_by` int(11) unsigned NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    <PERSON>EY `project_id` (`project_id`),
    <PERSON>EY `payment_status` (`payment_status`),
    <PERSON><PERSON><PERSON> `payment_date` (`payment_date`),
    <PERSON><PERSON>Y `created_by` (`created_by`),
    CONSTRAINT `project_payments_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `project_payments_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
