-- SmartFlo Database Reset Script
-- This script will delete all data and reset auto-increment IDs
-- WARNING: This will permanently delete all data!

SET FOREIGN_KEY_CHECKS = 0;

-- Clear all project-related data first (due to foreign key constraints)
-- Use DELETE instead of TR<PERSON>CA<PERSON> for tables with foreign key constraints
DELETE FROM `task_files`;
DELETE FROM `task_revisions`;
DELETE FROM `project_timeline`;
DELETE FROM `project_tasks`;
DELETE FROM `projects`;

-- Clear user-related data (except admin user)
DELETE FROM `remember_tokens`;
DELETE FROM `password_resets`;
DELETE FROM `login_attempts`;

-- Clear other system tables
DELETE FROM `user_roles`;

-- Reset auto-increment counters
ALTER TABLE `projects` AUTO_INCREMENT = 1;
ALTER TABLE `project_tasks` AUTO_INCREMENT = 1;
ALTER TABLE `project_timeline` AUTO_INCREMENT = 1;
ALTER TABLE `task_files` AUTO_INCREMENT = 1;
<PERSON><PERSON><PERSON> TABLE `task_revisions` AUTO_INCREMENT = 1;
<PERSON><PERSON><PERSON> TABLE `remember_tokens` AUTO_INCREMENT = 1;
<PERSON><PERSON>R TABLE `password_resets` AUTO_INCREMENT = 1;
ALTER TABLE `login_attempts` AUTO_INCREMENT = 1;
ALTER TABLE `user_roles` AUTO_INCREMENT = 1;

-- Keep admin user but reset other users
DELETE FROM `users` WHERE `id` > 1;
ALTER TABLE `users` AUTO_INCREMENT = 2;

-- Re-assign admin role to admin user (ID 1)
INSERT INTO `user_roles` (`user_id`, `role_id`, `created_at`) VALUES
(1, 1, NOW());

-- Reset task types to default values
DELETE FROM `task_types`;
ALTER TABLE `task_types` AUTO_INCREMENT = 1;

-- Insert default task types
INSERT INTO `task_types` (`name`, `description`, `color`, `icon`, `estimated_hours`, `is_default`, `created_at`, `updated_at`) VALUES
('Research & Planning', 'Initial research and project planning phase', '#3498db', 'fas fa-search', 8, 1, NOW(), NOW()),
('Design', 'UI/UX design and mockups', '#e74c3c', 'fas fa-paint-brush', 16, 1, NOW(), NOW()),
('Development', 'Core development and coding', '#2ecc71', 'fas fa-code', 40, 1, NOW(), NOW()),
('Testing', 'Quality assurance and testing', '#f39c12', 'fas fa-bug', 8, 1, NOW(), NOW()),
('Deployment', 'Final deployment and launch', '#9b59b6', 'fas fa-rocket', 4, 1, NOW(), NOW()),
('Review', 'Client review and feedback', '#1abc9c', 'fas fa-eye', 2, 1, NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- Display reset confirmation
SELECT 'Database has been reset successfully!' as Status;
SELECT 'All project data has been cleared.' as Message;
SELECT 'Admin user (admin/Admin@123) has been preserved.' as AdminInfo;
SELECT 'Auto-increment IDs have been reset to 1.' as AutoIncrement;
