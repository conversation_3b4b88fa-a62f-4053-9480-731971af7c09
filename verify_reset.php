<?php
/**
 * Verification script to test database reset
 * This script will check the database state after reset
 */

// Load database configuration
if (file_exists('.env')) {
    $env = file_get_contents('.env');
    $lines = explode("\n", $env);

    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");
            $_ENV[$key] = $value;
        }
    }
}

$config = [
    'host' => $_ENV['database.default.hostname'] ?? 'localhost',
    'port' => $_ENV['database.default.port'] ?? 3306,
    'database' => $_ENV['database.default.database'] ?? 'smartflo_auth',
    'username' => $_ENV['database.default.username'] ?? 'root',
    'password' => $_ENV['database.default.password'] ?? ''
];

// Connect to database
try {
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (PDOException $e) {
    die("❌ Database connection failed: " . $e->getMessage() . "\n");
}

echo "SmartFlo Database Reset Verification\n";
echo "====================================\n\n";

try {
    // Check current state
    echo "📊 Current Database State:\n";
    echo str_repeat("-", 30) . "\n";

    $tables = ['projects', 'project_tasks', 'users'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $stmt->fetch()['count'];
        echo sprintf("%-15s: %d records\n", ucfirst($table), $count);
    }

    // Check auto-increment values
    echo "\n🔢 Auto-increment Values:\n";
    echo str_repeat("-", 30) . "\n";

    foreach (['projects', 'project_tasks'] as $table) {
        $stmt = $pdo->query("SHOW TABLE STATUS LIKE '$table'");
        $status = $stmt->fetch();
        echo sprintf("%-15s: Next ID = %d\n", ucfirst($table), $status['Auto_increment'] ?? 'N/A');
    }

    echo "\n✅ Database reset verification complete!\n";
    echo "\nNext steps:\n";
    echo "1. Go to http://localhost:8080/projects\n";
    echo "2. Create a new project\n";
    echo "3. Add tasks to the project\n";
    echo "4. The first project should have ID = 1\n";
    echo "5. The first task should have ID = 1\n";
    echo "6. Try the 'Send for Client Review' modal - it should work now!\n\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
