<?php
/**
 * SmartFlo Database Reset Script
 * 
 * This script will reset the database to a clean state by:
 * - Deleting all project data
 * - Deleting all user data except admin
 * - Resetting auto-increment IDs
 * - Preserving admin user and roles
 * 
 * WARNING: This will permanently delete all data!
 */

require_once 'vendor/autoload.php';

class DatabaseReset
{
    private $pdo;
    private $config;

    public function __construct()
    {
        $this->loadConfig();
        $this->connectDatabase();
    }

    private function loadConfig()
    {
        // Load database configuration
        if (file_exists('.env')) {
            $env = file_get_contents('.env');
            $lines = explode("\n", $env);
            
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    list($key, $value) = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value, " \t\n\r\0\x0B\"'");
                    $_ENV[$key] = $value;
                }
            }
        }

        $this->config = [
            'host' => $_ENV['database.default.hostname'] ?? 'localhost',
            'port' => $_ENV['database.default.port'] ?? 3306,
            'database' => $_ENV['database.default.database'] ?? 'smartflo_auth',
            'username' => $_ENV['database.default.username'] ?? 'root',
            'password' => $_ENV['database.default.password'] ?? ''
        ];
    }

    private function connectDatabase()
    {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset=utf8mb4";
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            echo "✓ Connected to database: {$this->config['database']}\n";
        } catch (PDOException $e) {
            die("❌ Database connection failed: " . $e->getMessage() . "\n");
        }
    }

    public function confirmReset()
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "⚠️  WARNING: DATABASE RESET OPERATION\n";
        echo str_repeat("=", 60) . "\n";
        echo "This will permanently delete ALL data including:\n";
        echo "• All projects and tasks\n";
        echo "• All user accounts (except admin)\n";
        echo "• All files and attachments\n";
        echo "• All project history and timelines\n";
        echo "\nThe following will be preserved:\n";
        echo "• Admin user (admin/Admin@123)\n";
        echo "• System roles and permissions\n";
        echo "• Default task types\n";
        echo "\nAuto-increment IDs will be reset to 1.\n";
        echo str_repeat("=", 60) . "\n";
        
        echo "\nType 'RESET' to confirm (case sensitive): ";
        $handle = fopen("php://stdin", "r");
        $confirmation = trim(fgets($handle));
        fclose($handle);
        
        if ($confirmation !== 'RESET') {
            echo "❌ Reset cancelled. No changes made.\n";
            exit(0);
        }
        
        echo "\n✓ Confirmation received. Starting reset...\n\n";
    }

    public function resetDatabase()
    {
        try {
            // Read and execute the SQL reset script
            $sqlFile = 'database/reset_database.sql';
            
            if (!file_exists($sqlFile)) {
                throw new Exception("Reset SQL file not found: $sqlFile");
            }
            
            $sql = file_get_contents($sqlFile);
            
            // Split SQL into individual statements
            $statements = array_filter(
                array_map('trim', explode(';', $sql)),
                function($stmt) {
                    return !empty($stmt) && 
                           !preg_match('/^\s*--/', $stmt) && 
                           !preg_match('/^\s*SELECT.*as\s+(Status|Message|AdminInfo|AutoIncrement)/i', $stmt);
                }
            );
            
            echo "Executing database reset...\n";
            
            foreach ($statements as $statement) {
                if (!empty(trim($statement))) {
                    try {
                        $this->pdo->exec($statement);
                        
                        // Log specific operations
                        if (preg_match('/TRUNCATE TABLE\s+`?(\w+)`?/i', $statement, $matches)) {
                            echo "  ✓ Cleared table: {$matches[1]}\n";
                        } elseif (preg_match('/ALTER TABLE\s+`?(\w+)`?\s+AUTO_INCREMENT/i', $statement, $matches)) {
                            echo "  ✓ Reset auto-increment: {$matches[1]}\n";
                        } elseif (preg_match('/INSERT INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                            echo "  ✓ Inserted default data: {$matches[1]}\n";
                        }
                    } catch (PDOException $e) {
                        echo "  ⚠️  Warning: " . $e->getMessage() . "\n";
                    }
                }
            }
            
            echo "\n✅ Database reset completed successfully!\n\n";
            $this->showStatus();
            
        } catch (Exception $e) {
            echo "❌ Reset failed: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    private function showStatus()
    {
        try {
            // Show current status
            echo "📊 Current Database Status:\n";
            echo str_repeat("-", 40) . "\n";
            
            $tables = [
                'users' => 'Users',
                'projects' => 'Projects', 
                'project_tasks' => 'Tasks',
                'task_files' => 'Task Files',
                'project_timeline' => 'Timeline Entries'
            ];
            
            foreach ($tables as $table => $label) {
                try {
                    $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM `$table`");
                    $count = $stmt->fetch()['count'];
                    echo sprintf("%-20s: %d\n", $label, $count);
                } catch (PDOException $e) {
                    echo sprintf("%-20s: Table not found\n", $label);
                }
            }
            
            echo "\n🔐 Admin Login:\n";
            echo "Username: admin\n";
            echo "Password: Admin@123\n";
            echo "URL: http://localhost:8080\n";
            
        } catch (Exception $e) {
            echo "Could not retrieve status: " . $e->getMessage() . "\n";
        }
    }
}

// Main execution
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

echo "SmartFlo Database Reset Tool\n";
echo "============================\n";

$reset = new DatabaseReset();
$reset->confirmReset();
$reset->resetDatabase();

echo "\n🎉 Reset complete! You can now create fresh projects and tasks.\n";
echo "The task IDs will start from 1 and increment properly.\n\n";
