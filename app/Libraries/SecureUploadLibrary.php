<?php

namespace App\Libraries;

use Config\Upload;
use CodeIgniter\Files\File;
use CodeIgniter\HTTP\Files\UploadedFile;
use Exception;

class SecureUploadLibrary
{
    protected $config;
    protected $errors = [];
    protected $uploadedFiles = [];

    public function __construct()
    {
        $this->config = new Upload();
        $this->createUploadDirectories();
    }

    /**
     * Validate and upload file securely
     */
    public function uploadFile(UploadedFile $file, string $type = 'images', array $options = []): array
    {
        $this->errors = [];

        try {
            // Basic validation
            if (!$this->validateFile($file, $type)) {
                return ['success' => false, 'errors' => $this->errors];
            }

            // Security checks
            if (!$this->performSecurityChecks($file)) {
                return ['success' => false, 'errors' => $this->errors];
            }

            // Generate secure filename
            $filename = $this->generateSecureFilename($file);
            
            // Get upload path
            $uploadPath = $this->config->uploadPaths[$type];
            
            // Move file to secure location
            if (!$file->move($uploadPath, $filename)) {
                $this->errors[] = 'Failed to move uploaded file';
                return ['success' => false, 'errors' => $this->errors];
            }

            $filePath = $uploadPath . $filename;

            // Process image if enabled
            $processedFiles = [];
            if ($this->config->enableImageProcessing && $this->isImage($file)) {
                $processedFiles = $this->processImage($filePath, $options);
            }

            // Log upload activity
            if ($this->config->enableAuditLog) {
                $this->logUploadActivity($filename, $type, $file->getSize());
            }

            return [
                'success' => true,
                'filename' => $filename,
                'path' => $filePath,
                'size' => $file->getSize(),
                'type' => $file->getMimeType(),
                'processed_files' => $processedFiles
            ];

        } catch (Exception $e) {
            $this->errors[] = 'Upload failed: ' . $e->getMessage();
            return ['success' => false, 'errors' => $this->errors];
        }
    }

    /**
     * Validate uploaded file
     */
    protected function validateFile(UploadedFile $file, string $type): bool
    {
        // Check if file was uploaded
        if (!$file->isValid()) {
            $this->errors[] = 'Invalid file upload';
            return false;
        }

        // Check file size
        if ($file->getSize() > $this->config->maxSize) {
            $this->errors[] = 'File size exceeds maximum allowed size';
            return false;
        }

        // Check file extension
        $extension = strtolower($file->getClientExtension());
        $allowedTypes = $this->config->allowedTypes[$type] ?? $this->config->allowedTypes['images'];
        if (!in_array($extension, $allowedTypes)) {
            $this->errors[] = 'File type not allowed';
            return false;
        }

        // Check MIME type
        $mimeType = $file->getMimeType();
        $allowedMimes = $this->config->allowedMimes[$type] ?? $this->config->allowedMimes['images'];
        if (!in_array($mimeType, $allowedMimes)) {
            $this->errors[] = 'Invalid file format';
            return false;
        }

        // Check blacklisted extensions
        if (in_array($extension, $this->config->blacklistedExtensions)) {
            $this->errors[] = 'File type is blacklisted for security reasons';
            return false;
        }

        return true;
    }

    /**
     * Perform security checks on uploaded file
     */
    protected function performSecurityChecks(UploadedFile $file): bool
    {
        // Check for embedded PHP code in images
        if ($this->isImage($file)) {
            $content = file_get_contents($file->getTempName());
            if (preg_match('/<\?php|<\?=|<script/i', $content)) {
                $this->errors[] = 'File contains suspicious content';
                return false;
            }
        }

        // Virus scanning if enabled
        if ($this->config->enableVirusScanning) {
            if (!$this->scanForViruses($file)) {
                return false;
            }
        }

        // Check file headers
        if (!$this->validateFileHeaders($file)) {
            return false;
        }

        return true;
    }

    /**
     * Generate secure filename
     */
    protected function generateSecureFilename(UploadedFile $file): string
    {
        $extension = strtolower($file->getClientExtension());
        
        switch ($this->config->namingStrategy) {
            case 'timestamp':
                return time() . '_' . uniqid() . '.' . $extension;
            case 'original':
                return preg_replace('/[^a-zA-Z0-9._-]/', '', $file->getClientName());
            case 'random':
            default:
                return bin2hex(random_bytes(16)) . '.' . $extension;
        }
    }

    /**
     * Check if file is an image
     */
    protected function isImage(UploadedFile $file): bool
    {
        $imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return in_array($file->getMimeType(), $imageTypes);
    }

    /**
     * Process image (resize, optimize, generate thumbnails)
     */
    protected function processImage(string $filePath, array $options = []): array
    {
        $processedFiles = [];
        
        try {
            $image = \Config\Services::image();
            
            // Generate thumbnails if enabled
            if ($this->config->generateThumbnails) {
                foreach ($this->config->thumbnailSizes as $size => $dimensions) {
                    $thumbnailPath = $this->generateThumbnailPath($filePath, $size);
                    
                    $image->withFile($filePath)
                          ->resize($dimensions['width'], $dimensions['height'], true, 'center')
                          ->save($thumbnailPath, $this->config->imageQuality);
                    
                    $processedFiles['thumbnails'][$size] = $thumbnailPath;
                }
            }

            // Optimize original image
            $image->withFile($filePath)->save($filePath, $this->config->imageQuality);
            
        } catch (Exception $e) {
            // Log error but don't fail the upload
            log_message('error', 'Image processing failed: ' . $e->getMessage());
        }

        return $processedFiles;
    }

    /**
     * Generate thumbnail path
     */
    protected function generateThumbnailPath(string $originalPath, string $size): string
    {
        $pathInfo = pathinfo($originalPath);
        return $pathInfo['dirname'] . '/thumbs/' . $size . '_' . $pathInfo['basename'];
    }

    /**
     * Validate file headers
     */
    protected function validateFileHeaders(UploadedFile $file): bool
    {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file->getTempName());
        finfo_close($finfo);

        if ($mimeType !== $file->getMimeType()) {
            $this->errors[] = 'File header does not match file extension';
            return false;
        }

        return true;
    }

    /**
     * Scan for viruses (requires ClamAV)
     */
    protected function scanForViruses(UploadedFile $file): bool
    {
        // This would require ClamAV to be installed
        // For now, we'll just return true
        // In production, implement actual virus scanning
        return true;
    }

    /**
     * Create upload directories if they don't exist
     */
    protected function createUploadDirectories(): void
    {
        foreach ($this->config->uploadPaths as $path) {
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
                // Create index.html to prevent directory listing
                file_put_contents($path . 'index.html', $this->getDirectoryProtectionHTML());
            }
            
            // Create thumbnails directory
            $thumbsPath = $path . 'thumbs/';
            if (!is_dir($thumbsPath)) {
                mkdir($thumbsPath, 0755, true);
                file_put_contents($thumbsPath . 'index.html', $this->getDirectoryProtectionHTML());
            }
        }

        // Create quarantine directory
        if (!is_dir($this->config->quarantinePath)) {
            mkdir($this->config->quarantinePath, 0755, true);
            file_put_contents($this->config->quarantinePath . 'index.html', $this->getDirectoryProtectionHTML());
        }
    }

    /**
     * Get directory protection HTML
     */
    protected function getDirectoryProtectionHTML(): string
    {
        return '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><p>Directory access is forbidden.</p></body></html>';
    }

    /**
     * Log upload activity
     */
    protected function logUploadActivity(string $filename, string $type, int $size): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'filename' => $filename,
            'type' => $type,
            'size' => $size,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        log_message('info', 'File uploaded: ' . json_encode($logData));
    }

    /**
     * Get upload errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }
}
