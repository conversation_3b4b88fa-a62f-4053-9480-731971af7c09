<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateProjectPaymentsTable extends Migration
{
    public function up()
    {
        // Create project_payments table if it doesn't exist
        if (!$this->db->tableExists('project_payments')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'project_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'payment_amount' => [
                    'type' => 'DECIMAL',
                    'constraint' => '15,2',
                    'null' => false,
                ],
                'payment_method' => [
                    'type' => 'ENUM',
                    'constraint' => ['cash', 'check', 'bank_transfer', 'credit_card', 'other'],
                    'default' => 'cash',
                ],
                'payment_status' => [
                    'type' => 'ENUM',
                    'constraint' => ['pending', 'partial', 'completed', 'overdue'],
                    'default' => 'pending',
                ],
                'payment_date' => [
                    'type' => 'DATE',
                    'null' => true,
                ],
                'payment_notes' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'invoice_number' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => true,
                ],
                'created_by' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addKey('project_id');
            $this->forge->addKey('payment_status');
            $this->forge->addKey('payment_date');
            $this->forge->addKey('created_by');
            
            // Foreign key constraints
            $this->forge->addForeignKey('project_id', 'projects', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('created_by', 'users', 'id', 'CASCADE', 'CASCADE');
            
            $this->forge->createTable('project_payments');
        }
    }

    public function down()
    {
        $this->forge->dropTable('project_payments', true);
    }
}
