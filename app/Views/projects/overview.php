<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Projects Overview
                    </h4>
                    <div class="d-flex gap-2">
                        <a href="/projects" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-1"></i>Project List
                        </a>
                        <a href="/projects/create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>New Project
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($projects)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Projects Found</h5>
                            <p class="text-muted">Create your first project to get started.</p>
                            <a href="/projects/create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Create Project
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Project Name</th>
                                        <th>Client</th>
                                        <th>Status</th>
                                        <th class="text-end">Billed Amount</th>
                                        <th class="text-end">Received Amount</th>
                                        <th class="text-end">Balance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($projects as $project): ?>
                                        <tr>
                                            <td>
                                                <a href="/projects/view/<?= $project['id'] ?>" class="text-decoration-none fw-medium">
                                                    <?= esc($project['project_name']) ?>
                                                </a>
                                                <br>
                                                <small class="text-muted"><?= esc($project['project_id'] ?? 'N/A') ?></small>
                                            </td>
                                            <td>
                                                <?= esc($project['client_name']) ?>
                                                <br>
                                                <small class="text-muted"><?= esc($project['location']) ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $statusColors = [
                                                    'not_started' => 'secondary',
                                                    'in_progress' => 'primary',
                                                    'on_hold' => 'warning',
                                                    'completed' => 'success'
                                                ];
                                                $statusColor = $statusColors[$project['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $statusColor ?>">
                                                    <?= ucwords(str_replace('_', ' ', $project['status'])) ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <span class="fw-medium">
                                                    ₹<?= number_format($project['billed_amount'], 2) ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <span class="fw-medium text-success">
                                                    ₹<?= number_format($project['received_amount'], 2) ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <?php $balance = $project['balance']; ?>
                                                <span class="fw-medium <?= $balance > 0 ? 'text-danger' : 'text-success' ?>">
                                                    ₹<?= number_format($balance, 2) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="/projects/view/<?= $project['id'] ?>">
                                                                <i class="fas fa-eye me-2"></i>View Details
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="/projects/edit/<?= $project['id'] ?>">
                                                                <i class="fas fa-edit me-2"></i>Edit
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="addPayment(<?= $project['id'] ?>)">
                                                                <i class="fas fa-plus-circle me-2"></i>Add Payment
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="generateInvoice(<?= $project['id'] ?>)">
                                                                <i class="fas fa-file-invoice me-2"></i>Generate Invoice
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="generatePartPayment(<?= $project['id'] ?>)">
                                                                <i class="fas fa-file-invoice-dollar me-2"></i>Generate Part Payment
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="shareProject(<?= $project['id'] ?>)">
                                                                <i class="fas fa-share me-2"></i>Share
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary Cards -->
                        <div class="row mt-4">
                            <?php
                            $totalBilled = array_sum(array_column($projects, 'billed_amount'));
                            $totalReceived = array_sum(array_column($projects, 'received_amount'));
                            $totalBalance = $totalBilled - $totalReceived;
                            ?>
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Total Billed</h5>
                                        <h3 class="mb-0">₹<?= number_format($totalBilled, 2) ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Total Received</h5>
                                        <h3 class="mb-0">₹<?= number_format($totalReceived, 2) ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-<?= $totalBalance > 0 ? 'danger' : 'success' ?> text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Total Balance</h5>
                                        <h3 class="mb-0">₹<?= number_format($totalBalance, 2) ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function addPayment(projectId) {
    // TODO: Implement add payment modal
    alert('Add Payment functionality will be implemented');
}

function generateInvoice(projectId) {
    // TODO: Implement generate invoice
    alert('Generate Invoice functionality will be implemented');
}

function generatePartPayment(projectId) {
    // TODO: Implement generate part payment
    alert('Generate Part Payment functionality will be implemented');
}

function shareProject(projectId) {
    // TODO: Implement share project
    alert('Share Project functionality will be implemented');
}
</script>
<?= $this->endSection() ?>
