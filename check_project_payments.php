<?php
require_once 'vendor/autoload.php';

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Get database connection
$db = \Config\Database::connect();

// Check if project_payments table exists
$tableExists = $db->tableExists('project_payments');

if ($tableExists) {
    echo "✓ project_payments table already exists\n";
    
    // Show table structure
    $query = $db->query("DESCRIBE project_payments");
    $fields = $query->getResultArray();
    
    echo "\nTable structure:\n";
    foreach ($fields as $field) {
        echo "- {$field['Field']}: {$field['Type']}\n";
    }
} else {
    echo "✗ project_payments table does not exist\n";
    echo "Creating table...\n";
    
    // Create the table
    $sql = "CREATE TABLE IF NOT EXISTS `project_payments` (
        `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
        `project_id` int(11) unsigned NOT NULL,
        `payment_amount` decimal(15,2) NOT NULL,
        `payment_method` enum('cash','check','bank_transfer','credit_card','other') DEFAULT 'cash',
        `payment_status` enum('pending','partial','completed','overdue') DEFAULT 'pending',
        `payment_date` date DEFAULT NULL,
        `payment_notes` text DEFAULT NULL,
        `invoice_number` varchar(100) DEFAULT NULL,
        `created_by` int(11) unsigned NOT NULL,
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `project_id` (`project_id`),
        KEY `payment_status` (`payment_status`),
        KEY `payment_date` (`payment_date`),
        KEY `created_by` (`created_by`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    try {
        $db->query($sql);
        echo "✓ Table created successfully\n";
        
        // Try to add foreign keys (may fail if referenced tables don't exist)
        try {
            $db->query("ALTER TABLE `project_payments` ADD CONSTRAINT `project_payments_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE");
            echo "✓ Foreign key constraint for project_id added\n";
        } catch (Exception $e) {
            echo "⚠ Could not add project_id foreign key: " . $e->getMessage() . "\n";
        }
        
        try {
            $db->query("ALTER TABLE `project_payments` ADD CONSTRAINT `project_payments_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE");
            echo "✓ Foreign key constraint for created_by added\n";
        } catch (Exception $e) {
            echo "⚠ Could not add created_by foreign key: " . $e->getMessage() . "\n";
        }
        
    } catch (Exception $e) {
        echo "✗ Error creating table: " . $e->getMessage() . "\n";
    }
}
?>
